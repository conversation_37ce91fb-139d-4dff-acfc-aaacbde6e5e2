/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

/* 屏幕管理 */
.screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.screen.active {
    display: flex;
    flex-direction: column;
}

.screen.overlay {
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    margin: 5px;
}

.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-danger {
    background: linear-gradient(45deg, #f44336, #d32f2f);
    color: white;
}

.btn-hint {
    background: linear-gradient(45deg, #FF9800, #F57C00);
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 主菜单样式 */
#main-menu {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    max-width: 800px;
    width: 90%;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.game-title h1 {
    font-size: 3em;
    color: #4CAF50;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-title p {
    font-size: 1.2em;
    color: #666;
    margin-bottom: 30px;
}

.score-display {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.total-score {
    font-size: 1.5em;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.level-selection h2 {
    margin-bottom: 20px;
    color: #333;
}

.level-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.level-btn {
    aspect-ratio: 1;
    border: 3px solid #ddd;
    border-radius: 10px;
    background: white;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.level-btn:hover {
    transform: scale(1.05);
    border-color: #4CAF50;
}

.level-btn.completed {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border-color: #4CAF50;
}

.level-btn.locked {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
    border-color: #ccc;
}

.level-btn.current {
    border-color: #FF9800;
    background: linear-gradient(45deg, #FFE0B2, #FFCC02);
}

/* 游戏界面样式 */
#game-screen {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.game-info {
    display: flex;
    gap: 30px;
    align-items: center;
}

.game-info > div {
    background: rgba(76, 175, 80, 0.1);
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: bold;
}

.timer {
    background: rgba(255, 152, 0, 0.1) !important;
    color: #FF9800;
}

.timer.warning {
    background: rgba(244, 67, 54, 0.1) !important;
    color: #f44336;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.images-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 20px;
}

.image-wrapper {
    text-align: center;
    flex: 1;
    max-width: 500px;
}

.game-canvas {
    width: 100%;
    max-width: 500px;
    height: auto;
    border: 3px solid #ddd;
    border-radius: 10px;
    cursor: crosshair;
    display: block;
    margin: 0 auto;
}

.image-label {
    margin-top: 10px;
    font-weight: bold;
    color: #666;
    font-size: 1.1em;
}

.game-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.differences-found {
    background: rgba(76, 175, 80, 0.1);
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: bold;
    color: #4CAF50;
}

/* 弹窗样式 */
.pause-content,
.victory-content,
.game-over-content,
.loading-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.victory-stats {
    margin: 20px 0;
    text-align: left;
}

.stat {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.stat.total {
    border-top: 2px solid #4CAF50;
    border-bottom: none;
    font-weight: bold;
    font-size: 1.2em;
    color: #4CAF50;
}

.victory-buttons,
.game-over-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 20px;
}

/* 加载动画 */
.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 差异标记样式 */
.difference-mark {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid #ff0000;
    border-radius: 50%;
    background: rgba(255, 0, 0, 0.2);
    pointer-events: none;
    animation: found-animation 0.5s ease-out;
}

@keyframes found-animation {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}

@keyframes miss-animation {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .images-container {
        flex-direction: column;
        gap: 15px;
    }

    .game-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .game-info {
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }

    .level-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .game-title h1 {
        font-size: 2em;
    }

    #main-menu {
        padding: 20px;
    }

    .victory-buttons,
    .game-over-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .level-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .game-controls {
        flex-direction: column;
        gap: 10px;
    }

    .btn {
        width: 100%;
        max-width: 200px;
    }
}
