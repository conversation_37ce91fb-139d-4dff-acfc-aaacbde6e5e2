<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>找茬小游戏</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- 游戏主界面 -->
    <div id="main-menu" class="screen active">
        <div class="game-title">
            <h1>找茬大挑战</h1>
            <p>找出两张图片的不同之处</p>
        </div>
        <div class="score-display">
            <div class="total-score">总积分: <span id="total-score">0</span></div>
        </div>
        <div class="level-selection">
            <h2>选择关卡</h2>
            <div class="level-grid" id="level-grid">
                <!-- 关卡按钮将通过JavaScript生成 -->
            </div>
        </div>
        <button id="start-game" class="btn btn-primary">开始游戏</button>
    </div>

    <!-- 游戏界面 -->
    <div id="game-screen" class="screen">
        <div class="game-header">
            <div class="game-info">
                <div class="level-info">
                    <span>第<span id="current-major">1</span>大关 - 第<span id="current-minor">1</span>小关</span>
                </div>
                <div class="timer">
                    <span>时间: <span id="countdown">60</span>s</span>
                </div>
                <div class="score">
                    <span>积分: <span id="current-score">0</span></span>
                </div>
            </div>
            <button id="back-to-menu" class="btn btn-secondary">返回菜单</button>
        </div>

        <div class="game-content">
            <div class="images-container">
                <div class="image-wrapper">
                    <canvas id="left-canvas" class="game-canvas"></canvas>
                    <div class="image-label">原图</div>
                </div>
                <div class="image-wrapper">
                    <canvas id="right-canvas" class="game-canvas"></canvas>
                    <div class="image-label">找茬图</div>
                </div>
            </div>
            
            <div class="game-controls">
                <div class="differences-found">
                    <span>已找到: <span id="found-count">0</span>/<span id="total-differences">5</span></span>
                </div>
                <button id="hint-btn" class="btn btn-hint">提示 (<span id="hint-count">3</span>)</button>
                <button id="pause-btn" class="btn btn-secondary">暂停</button>
            </div>
        </div>
    </div>

    <!-- 暂停界面 -->
    <div id="pause-screen" class="screen overlay">
        <div class="pause-content">
            <h2>游戏暂停</h2>
            <button id="resume-btn" class="btn btn-primary">继续游戏</button>
            <button id="restart-level" class="btn btn-secondary">重新开始</button>
            <button id="quit-game" class="btn btn-danger">退出游戏</button>
        </div>
    </div>

    <!-- 通关界面 -->
    <div id="victory-screen" class="screen overlay">
        <div class="victory-content">
            <h2 id="victory-title">恭喜通关！</h2>
            <div class="victory-stats">
                <div class="stat">
                    <span class="label">用时:</span>
                    <span id="time-used">0</span>秒
                </div>
                <div class="stat">
                    <span class="label">本关积分:</span>
                    <span id="level-score">0</span>
                </div>
                <div class="stat">
                    <span class="label">奖励积分:</span>
                    <span id="bonus-score">0</span>
                </div>
                <div class="stat total">
                    <span class="label">总积分:</span>
                    <span id="final-score">0</span>
                </div>
            </div>
            <div class="victory-buttons">
                <button id="next-level" class="btn btn-primary">下一关</button>
                <button id="replay-level" class="btn btn-secondary">重玩本关</button>
                <button id="back-menu" class="btn btn-secondary">返回菜单</button>
            </div>
        </div>
    </div>

    <!-- 游戏结束界面 -->
    <div id="game-over-screen" class="screen overlay">
        <div class="game-over-content">
            <h2>时间到！</h2>
            <p>很遗憾，没有在规定时间内完成挑战</p>
            <div class="game-over-buttons">
                <button id="retry-level" class="btn btn-primary">重试</button>
                <button id="back-to-main" class="btn btn-secondary">返回菜单</button>
            </div>
        </div>
    </div>

    <!-- 加载界面 -->
    <div id="loading-screen" class="screen overlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>加载中...</p>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/game.js"></script>
</body>
</html>
