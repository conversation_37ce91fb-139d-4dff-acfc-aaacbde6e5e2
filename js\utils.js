// 工具函数集合

/**
 * 本地存储管理
 */
const Storage = {
    // 保存游戏数据
    save: function(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (e) {
            console.error('保存数据失败:', e);
        }
    },
    
    // 读取游戏数据
    load: function(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (e) {
            console.error('读取数据失败:', e);
            return defaultValue;
        }
    },
    
    // 删除数据
    remove: function(key) {
        try {
            localStorage.removeItem(key);
        } catch (e) {
            console.error('删除数据失败:', e);
        }
    }
};

/**
 * 音效管理
 */
const SoundManager = {
    sounds: {},
    enabled: true,
    
    // 加载音效
    load: function(name, url) {
        if (!this.enabled) return;
        try {
            this.sounds[name] = new Audio(url);
            this.sounds[name].preload = 'auto';
        } catch (e) {
            console.warn('音效加载失败:', name, e);
        }
    },
    
    // 播放音效
    play: function(name, volume = 1) {
        if (!this.enabled || !this.sounds[name]) return;
        try {
            const sound = this.sounds[name].cloneNode();
            sound.volume = volume;
            sound.play().catch(e => console.warn('音效播放失败:', name, e));
        } catch (e) {
            console.warn('音效播放失败:', name, e);
        }
    },
    
    // 切换音效开关
    toggle: function() {
        this.enabled = !this.enabled;
        return this.enabled;
    }
};

/**
 * 动画工具
 */
const AnimationUtils = {
    // 缓动函数
    easeOutBounce: function(t) {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    },
    
    // 数字动画
    animateNumber: function(element, start, end, duration = 1000) {
        const startTime = Date.now();
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const current = Math.floor(start + (end - start) * this.easeOutBounce(progress));
            element.textContent = current;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        animate();
    },
    
    // 元素闪烁
    flash: function(element, times = 3, duration = 200) {
        let count = 0;
        const flash = () => {
            element.style.opacity = element.style.opacity === '0.5' ? '1' : '0.5';
            count++;
            if (count < times * 2) {
                setTimeout(flash, duration);
            } else {
                element.style.opacity = '1';
            }
        };
        flash();
    }
};

/**
 * 图片处理工具
 */
const ImageUtils = {
    // 加载图片
    loadImage: function(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = src;
        });
    },
    
    // 创建差异图片
    createDifferenceImage: function(originalCanvas, differences) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = originalCanvas.width;
        canvas.height = originalCanvas.height;
        
        // 复制原图
        ctx.drawImage(originalCanvas, 0, 0);
        
        // 添加差异
        differences.forEach(diff => {
            this.applyDifference(ctx, diff);
        });
        
        return canvas;
    },
    
    // 应用单个差异
    applyDifference: function(ctx, difference) {
        const { type, x, y, width, height, color, radius } = difference;
        
        switch (type) {
            case 'color':
                // 颜色差异
                ctx.fillStyle = color;
                ctx.fillRect(x, y, width, height);
                break;
                
            case 'circle':
                // 圆形差异
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, 2 * Math.PI);
                ctx.fillStyle = color;
                ctx.fill();
                break;
                
            case 'missing':
                // 缺失部分（用白色覆盖）
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(x, y, width, height);
                break;
                
            case 'extra':
                // 额外元素
                ctx.fillStyle = color;
                ctx.fillRect(x, y, width, height);
                break;
        }
    },
    
    // 检测点击是否在差异区域内
    isPointInDifference: function(x, y, difference) {
        const { type, x: dx, y: dy, width, height, radius } = difference;
        
        switch (type) {
            case 'color':
            case 'missing':
            case 'extra':
                return x >= dx && x <= dx + width && y >= dy && y <= dy + height;
                
            case 'circle':
                const distance = Math.sqrt((x - dx) ** 2 + (y - dy) ** 2);
                return distance <= radius;
                
            default:
                return false;
        }
    }
};

/**
 * 时间格式化工具
 */
const TimeUtils = {
    // 格式化秒数为 MM:SS 格式
    formatTime: function(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },
    
    // 格式化毫秒为可读格式
    formatDuration: function(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}小时${minutes % 60}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }
};

/**
 * 随机数工具
 */
const RandomUtils = {
    // 生成指定范围内的随机整数
    randomInt: function(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    // 生成随机颜色
    randomColor: function() {
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
        return colors[this.randomInt(0, colors.length - 1)];
    },
    
    // 打乱数组
    shuffle: function(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
};

/**
 * DOM 工具
 */
const DOMUtils = {
    // 显示元素
    show: function(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (element) {
            element.classList.add('active');
        }
    },
    
    // 隐藏元素
    hide: function(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (element) {
            element.classList.remove('active');
        }
    },
    
    // 切换元素显示状态
    toggle: function(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (element) {
            element.classList.toggle('active');
        }
    },
    
    // 创建差异标记
    createDifferenceMark: function(x, y, parent) {
        const mark = document.createElement('div');
        mark.className = 'difference-mark';
        mark.style.left = (x - 15) + 'px';
        mark.style.top = (y - 15) + 'px';
        parent.appendChild(mark);
        return mark;
    }
};

/**
 * 事件管理器
 */
const EventManager = {
    listeners: {},
    
    // 添加事件监听
    on: function(event, callback) {
        if (!this.listeners[event]) {
            this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
    },
    
    // 移除事件监听
    off: function(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
        }
    },
    
    // 触发事件
    emit: function(event, data) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (e) {
                    console.error('事件回调执行失败:', event, e);
                }
            });
        }
    }
};
