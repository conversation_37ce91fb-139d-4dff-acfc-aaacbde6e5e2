// 找茬游戏主逻辑

/**
 * 游戏配置
 */
const GameConfig = {
    // 关卡配置
    MAJOR_LEVELS: 5,        // 大关数量
    MINOR_LEVELS: 5,        // 每大关的小关数量
    DIFFERENCES_PER_LEVEL: 5, // 每关差异数量
    
    // 时间配置（秒）
    TIME_LIMITS: [60, 50, 40, 30, 20], // 各大关时间限制
    
    // 积分配置
    SCORE_PER_DIFFERENCE: 100,  // 每个差异的基础分数
    TIME_BONUS_MULTIPLIER: 2,   // 时间奖励倍数
    MAJOR_LEVEL_BONUS: 500,     // 大关通关奖励
    
    // 提示配置
    HINTS_PER_LEVEL: 3,         // 每关提示次数
    HINT_PENALTY: 50,           // 使用提示的扣分
    
    // 图片尺寸
    CANVAS_WIDTH: 500,
    CANVAS_HEIGHT: 375
};

/**
 * 游戏状态管理
 */
class GameState {
    constructor() {
        this.reset();
    }
    
    reset() {
        this.currentMajorLevel = 1;
        this.currentMinorLevel = 1;
        this.totalScore = Storage.load('totalScore', 0);
        this.currentLevelScore = 0;
        this.timeRemaining = 0;
        this.gameStartTime = 0;
        this.isPaused = false;
        this.isGameActive = false;
        this.hintsRemaining = GameConfig.HINTS_PER_LEVEL;
        this.foundDifferences = [];
        this.currentDifferences = [];
        this.completedLevels = Storage.load('completedLevels', []);
    }
    
    // 获取当前关卡的时间限制
    getCurrentTimeLimit() {
        return GameConfig.TIME_LIMITS[this.currentMajorLevel - 1];
    }
    
    // 检查关卡是否已完成
    isLevelCompleted(major, minor) {
        return this.completedLevels.some(level => 
            level.major === major && level.minor === minor
        );
    }
    
    // 标记关卡为已完成
    markLevelCompleted(major, minor, score) {
        const existingIndex = this.completedLevels.findIndex(level => 
            level.major === major && level.minor === minor
        );
        
        const levelData = { major, minor, score, timestamp: Date.now() };
        
        if (existingIndex >= 0) {
            // 更新已有记录（如果新分数更高）
            if (score > this.completedLevels[existingIndex].score) {
                this.completedLevels[existingIndex] = levelData;
            }
        } else {
            // 添加新记录
            this.completedLevels.push(levelData);
        }
        
        Storage.save('completedLevels', this.completedLevels);
    }
    
    // 保存总分
    saveTotalScore() {
        Storage.save('totalScore', this.totalScore);
    }
}

/**
 * 关卡数据生成器
 */
class LevelGenerator {
    constructor() {
        this.baseImages = this.generateBaseImages();
    }
    
    // 生成基础图片数据
    generateBaseImages() {
        const images = [];
        for (let major = 1; major <= GameConfig.MAJOR_LEVELS; major++) {
            for (let minor = 1; minor <= GameConfig.MINOR_LEVELS; minor++) {
                images.push({
                    major,
                    minor,
                    basePattern: this.generatePattern(major, minor)
                });
            }
        }
        return images;
    }
    
    // 生成图案
    generatePattern(major, minor) {
        const seed = major * 1000 + minor;
        const random = this.seededRandom(seed);
        
        return {
            backgroundColor: this.getRandomColor(random),
            shapes: this.generateShapes(random, 8 + major * 2),
            complexity: major
        };
    }
    
    // 种子随机数生成器
    seededRandom(seed) {
        let x = Math.sin(seed) * 10000;
        return () => {
            x = Math.sin(x) * 10000;
            return x - Math.floor(x);
        };
    }
    
    // 获取随机颜色
    getRandomColor(random) {
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', 
            '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
        ];
        return colors[Math.floor(random() * colors.length)];
    }
    
    // 生成形状
    generateShapes(random, count) {
        const shapes = [];
        const shapeTypes = ['circle', 'rectangle', 'triangle'];
        
        for (let i = 0; i < count; i++) {
            const type = shapeTypes[Math.floor(random() * shapeTypes.length)];
            shapes.push({
                type,
                x: random() * (GameConfig.CANVAS_WIDTH - 100) + 50,
                y: random() * (GameConfig.CANVAS_HEIGHT - 100) + 50,
                size: random() * 30 + 20,
                color: this.getRandomColor(random),
                rotation: random() * 360
            });
        }
        
        return shapes;
    }
    
    // 获取关卡数据
    getLevelData(major, minor) {
        const baseData = this.baseImages.find(img => 
            img.major === major && img.minor === minor
        );
        
        if (!baseData) return null;
        
        return {
            ...baseData,
            differences: this.generateDifferences(baseData, major, minor)
        };
    }
    
    // 生成差异
    generateDifferences(baseData, major, minor) {
        const seed = major * 10000 + minor * 100 + 999;
        const random = this.seededRandom(seed);
        const differences = [];
        const shapes = [...baseData.basePattern.shapes];
        
        for (let i = 0; i < GameConfig.DIFFERENCES_PER_LEVEL; i++) {
            const diffType = Math.floor(random() * 4);
            let difference;
            
            switch (diffType) {
                case 0: // 颜色差异
                    const shapeIndex = Math.floor(random() * shapes.length);
                    difference = {
                        type: 'color',
                        targetShape: shapeIndex,
                        newColor: this.getRandomColor(random),
                        x: shapes[shapeIndex].x - 25,
                        y: shapes[shapeIndex].y - 25,
                        width: 50,
                        height: 50
                    };
                    break;
                    
                case 1: // 缺失形状
                    const missingIndex = Math.floor(random() * shapes.length);
                    difference = {
                        type: 'missing',
                        targetShape: missingIndex,
                        x: shapes[missingIndex].x - 25,
                        y: shapes[missingIndex].y - 25,
                        width: 50,
                        height: 50
                    };
                    break;
                    
                case 2: // 额外形状
                    difference = {
                        type: 'extra',
                        shape: {
                            type: 'circle',
                            x: random() * (GameConfig.CANVAS_WIDTH - 100) + 50,
                            y: random() * (GameConfig.CANVAS_HEIGHT - 100) + 50,
                            size: random() * 20 + 15,
                            color: this.getRandomColor(random)
                        },
                        x: 0, y: 0, width: 30, height: 30
                    };
                    difference.x = difference.shape.x - 15;
                    difference.y = difference.shape.y - 15;
                    break;
                    
                case 3: // 大小差异
                    const sizeIndex = Math.floor(random() * shapes.length);
                    difference = {
                        type: 'size',
                        targetShape: sizeIndex,
                        newSize: shapes[sizeIndex].size * (0.5 + random() * 0.8),
                        x: shapes[sizeIndex].x - 25,
                        y: shapes[sizeIndex].y - 25,
                        width: 50,
                        height: 50
                    };
                    break;
            }
            
            differences.push(difference);
        }
        
        return differences;
    }
}

/**
 * 游戏渲染器
 */
class GameRenderer {
    constructor() {
        this.leftCanvas = document.getElementById('left-canvas');
        this.rightCanvas = document.getElementById('right-canvas');
        this.leftCtx = this.leftCanvas.getContext('2d');
        this.rightCtx = this.rightCanvas.getContext('2d');
        
        this.setupCanvases();
    }
    
    setupCanvases() {
        [this.leftCanvas, this.rightCanvas].forEach(canvas => {
            canvas.width = GameConfig.CANVAS_WIDTH;
            canvas.height = GameConfig.CANVAS_HEIGHT;
            canvas.style.maxWidth = '100%';
            canvas.style.height = 'auto';
        });
    }
    
    // 渲染关卡
    renderLevel(levelData) {
        // 渲染原图
        this.renderImage(this.leftCtx, levelData.basePattern);
        
        // 渲染找茬图
        this.renderImageWithDifferences(this.rightCtx, levelData.basePattern, levelData.differences);
    }
    
    // 渲染基础图像
    renderImage(ctx, pattern) {
        // 清空画布
        ctx.clearRect(0, 0, GameConfig.CANVAS_WIDTH, GameConfig.CANVAS_HEIGHT);
        
        // 绘制背景
        ctx.fillStyle = pattern.backgroundColor;
        ctx.fillRect(0, 0, GameConfig.CANVAS_WIDTH, GameConfig.CANVAS_HEIGHT);
        
        // 绘制形状
        pattern.shapes.forEach(shape => {
            this.drawShape(ctx, shape);
        });
    }
    
    // 渲染带差异的图像
    renderImageWithDifferences(ctx, pattern, differences) {
        // 先渲染基础图像
        this.renderImage(ctx, pattern);
        
        // 应用差异
        differences.forEach(diff => {
            this.applyDifference(ctx, pattern, diff);
        });
    }
    
    // 绘制形状
    drawShape(ctx, shape) {
        ctx.save();
        ctx.translate(shape.x, shape.y);
        ctx.rotate(shape.rotation * Math.PI / 180);
        ctx.fillStyle = shape.color;
        
        switch (shape.type) {
            case 'circle':
                ctx.beginPath();
                ctx.arc(0, 0, shape.size / 2, 0, 2 * Math.PI);
                ctx.fill();
                break;
                
            case 'rectangle':
                ctx.fillRect(-shape.size / 2, -shape.size / 2, shape.size, shape.size);
                break;
                
            case 'triangle':
                ctx.beginPath();
                ctx.moveTo(0, -shape.size / 2);
                ctx.lineTo(-shape.size / 2, shape.size / 2);
                ctx.lineTo(shape.size / 2, shape.size / 2);
                ctx.closePath();
                ctx.fill();
                break;
        }
        
        ctx.restore();
    }
    
    // 应用差异
    applyDifference(ctx, pattern, difference) {
        switch (difference.type) {
            case 'color':
                const colorShape = { ...pattern.shapes[difference.targetShape] };
                colorShape.color = difference.newColor;
                this.drawShape(ctx, colorShape);
                break;
                
            case 'missing':
                // 用背景色覆盖
                const missingShape = pattern.shapes[difference.targetShape];
                ctx.save();
                ctx.translate(missingShape.x, missingShape.y);
                ctx.fillStyle = pattern.backgroundColor;
                ctx.fillRect(-missingShape.size, -missingShape.size, 
                           missingShape.size * 2, missingShape.size * 2);
                ctx.restore();
                break;
                
            case 'extra':
                this.drawShape(ctx, difference.shape);
                break;
                
            case 'size':
                const sizeShape = { ...pattern.shapes[difference.targetShape] };
                sizeShape.size = difference.newSize;
                // 先清除原形状
                const originalShape = pattern.shapes[difference.targetShape];
                ctx.save();
                ctx.translate(originalShape.x, originalShape.y);
                ctx.fillStyle = pattern.backgroundColor;
                ctx.fillRect(-originalShape.size, -originalShape.size, 
                           originalShape.size * 2, originalShape.size * 2);
                ctx.restore();
                // 绘制新形状
                this.drawShape(ctx, sizeShape);
                break;
        }
    }
    
    // 标记找到的差异
    markDifference(canvas, x, y) {
        const rect = canvas.getBoundingClientRect();
        const scaleX = canvas.width / rect.width;
        const scaleY = canvas.height / rect.height;
        
        const mark = document.createElement('div');
        mark.className = 'difference-mark';
        mark.style.position = 'absolute';
        mark.style.left = (x / scaleX - 15) + 'px';
        mark.style.top = (y / scaleY - 15) + 'px';
        mark.style.pointerEvents = 'none';
        
        canvas.parentElement.style.position = 'relative';
        canvas.parentElement.appendChild(mark);
        
        return mark;
    }
}

/**
 * 主游戏类
 */
class SpotTheDifferenceGame {
    constructor() {
        this.state = new GameState();
        this.levelGenerator = new LevelGenerator();
        this.renderer = new GameRenderer();
        this.timer = null;
        this.currentLevelData = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.updateUI();
        this.generateLevelButtons();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 主菜单按钮
        document.getElementById('start-game').addEventListener('click', () => {
            this.startLevel(1, 1);
        });
        
        // 游戏控制按钮
        document.getElementById('back-to-menu').addEventListener('click', () => {
            this.backToMenu();
        });
        
        document.getElementById('pause-btn').addEventListener('click', () => {
            this.pauseGame();
        });
        
        document.getElementById('hint-btn').addEventListener('click', () => {
            this.useHint();
        });
        
        // 暂停界面按钮
        document.getElementById('resume-btn').addEventListener('click', () => {
            this.resumeGame();
        });
        
        document.getElementById('restart-level').addEventListener('click', () => {
            this.restartLevel();
        });
        
        document.getElementById('quit-game').addEventListener('click', () => {
            this.backToMenu();
        });
        
        // 通关界面按钮
        document.getElementById('next-level').addEventListener('click', () => {
            this.nextLevel();
        });
        
        document.getElementById('replay-level').addEventListener('click', () => {
            this.restartLevel();
        });
        
        document.getElementById('back-menu').addEventListener('click', () => {
            this.backToMenu();
        });
        
        // 游戏结束界面按钮
        document.getElementById('retry-level').addEventListener('click', () => {
            this.restartLevel();
        });
        
        document.getElementById('back-to-main').addEventListener('click', () => {
            this.backToMenu();
        });
        
        // 画布点击事件
        this.renderer.rightCanvas.addEventListener('click', (e) => {
            this.handleCanvasClick(e);
        });
    }
    
    // 生成关卡按钮
    generateLevelButtons() {
        const levelGrid = document.getElementById('level-grid');
        levelGrid.innerHTML = '';
        
        for (let major = 1; major <= GameConfig.MAJOR_LEVELS; major++) {
            for (let minor = 1; minor <= GameConfig.MINOR_LEVELS; minor++) {
                const button = document.createElement('button');
                button.className = 'level-btn';
                button.innerHTML = `<div>第${major}关</div><div>${minor}-${GameConfig.MINOR_LEVELS}</div>`;
                
                // 设置按钮状态
                if (this.state.isLevelCompleted(major, minor)) {
                    button.classList.add('completed');
                } else if (this.isLevelUnlocked(major, minor)) {
                    button.classList.add('current');
                    button.addEventListener('click', () => {
                        this.startLevel(major, minor);
                    });
                } else {
                    button.classList.add('locked');
                    button.disabled = true;
                }
                
                levelGrid.appendChild(button);
            }
        }
    }
    
    // 检查关卡是否解锁
    isLevelUnlocked(major, minor) {
        if (major === 1 && minor === 1) return true;
        
        // 检查前一关是否完成
        let prevMajor = major;
        let prevMinor = minor - 1;
        
        if (prevMinor < 1) {
            prevMajor--;
            prevMinor = GameConfig.MINOR_LEVELS;
        }
        
        if (prevMajor < 1) return false;
        
        return this.state.isLevelCompleted(prevMajor, prevMinor);
    }
    
    // 开始关卡
    startLevel(major, minor) {
        this.state.currentMajorLevel = major;
        this.state.currentMinorLevel = minor;
        this.state.timeRemaining = this.state.getCurrentTimeLimit();
        this.state.gameStartTime = Date.now();
        this.state.isGameActive = true;
        this.state.isPaused = false;
        this.state.hintsRemaining = GameConfig.HINTS_PER_LEVEL;
        this.state.foundDifferences = [];
        this.state.currentLevelScore = 0;
        
        // 获取关卡数据
        this.currentLevelData = this.levelGenerator.getLevelData(major, minor);
        this.state.currentDifferences = [...this.currentLevelData.differences];
        
        // 渲染关卡
        this.renderer.renderLevel(this.currentLevelData);
        
        // 更新UI
        this.updateGameUI();
        this.showScreen('game-screen');
        
        // 开始计时
        this.startTimer();
    }
    
    // 开始计时器
    startTimer() {
        this.timer = setInterval(() => {
            if (!this.state.isPaused && this.state.isGameActive) {
                this.state.timeRemaining--;
                this.updateTimerDisplay();
                
                if (this.state.timeRemaining <= 0) {
                    this.gameOver();
                }
            }
        }, 1000);
    }
    
    // 停止计时器
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    // 处理画布点击
    handleCanvasClick(e) {
        if (!this.state.isGameActive || this.state.isPaused) return;
        
        const rect = this.renderer.rightCanvas.getBoundingClientRect();
        const scaleX = this.renderer.rightCanvas.width / rect.width;
        const scaleY = this.renderer.rightCanvas.height / rect.height;
        
        const x = (e.clientX - rect.left) * scaleX;
        const y = (e.clientY - rect.top) * scaleY;
        
        // 检查是否点击到差异
        const foundDifference = this.checkDifference(x, y);
        
        if (foundDifference) {
            this.foundDifference(foundDifference, e.clientX - rect.left, e.clientY - rect.top);
        }
    }
    
    // 检查差异
    checkDifference(x, y) {
        return this.state.currentDifferences.find(diff => {
            const distance = Math.sqrt((x - (diff.x + diff.width/2))**2 + (y - (diff.y + diff.height/2))**2);
            return distance <= 30; // 30像素的容错范围
        });
    }
    
    // 找到差异
    foundDifference(difference, clickX, clickY) {
        // 移除已找到的差异
        this.state.currentDifferences = this.state.currentDifferences.filter(d => d !== difference);
        this.state.foundDifferences.push(difference);
        
        // 标记差异
        this.renderer.markDifference(this.renderer.rightCanvas, clickX, clickY);
        
        // 计算分数
        const baseScore = GameConfig.SCORE_PER_DIFFERENCE;
        const timeBonus = Math.floor(this.state.timeRemaining * GameConfig.TIME_BONUS_MULTIPLIER);
        const score = baseScore + timeBonus;
        this.state.currentLevelScore += score;
        
        // 更新UI
        this.updateGameUI();
        
        // 检查是否完成关卡
        if (this.state.currentDifferences.length === 0) {
            this.levelComplete();
        }
    }
    
    // 使用提示
    useHint() {
        if (this.state.hintsRemaining <= 0 || this.state.currentDifferences.length === 0) return;
        
        this.state.hintsRemaining--;
        this.state.currentLevelScore = Math.max(0, this.state.currentLevelScore - GameConfig.HINT_PENALTY);
        
        // 显示一个差异的提示
        const hintDifference = this.state.currentDifferences[0];
        const hintElement = document.createElement('div');
        hintElement.className = 'difference-mark';
        hintElement.style.position = 'absolute';
        hintElement.style.left = (hintDifference.x + hintDifference.width/2 - 15) + 'px';
        hintElement.style.top = (hintDifference.y + hintDifference.height/2 - 15) + 'px';
        hintElement.style.border = '3px solid #FFD700';
        hintElement.style.animation = 'pulse 2s ease-in-out 3';
        
        this.renderer.rightCanvas.parentElement.appendChild(hintElement);
        
        // 3秒后移除提示
        setTimeout(() => {
            hintElement.remove();
        }, 6000);
        
        this.updateGameUI();
    }
    
    // 关卡完成
    levelComplete() {
        this.state.isGameActive = false;
        this.stopTimer();
        
        // 计算最终分数
        const timeUsed = this.state.getCurrentTimeLimit() - this.state.timeRemaining;
        let bonusScore = 0;
        
        // 检查是否完成大关
        if (this.state.currentMinorLevel === GameConfig.MINOR_LEVELS) {
            bonusScore = GameConfig.MAJOR_LEVEL_BONUS;
        }
        
        const finalScore = this.state.currentLevelScore + bonusScore;
        this.state.totalScore += finalScore;
        
        // 保存进度
        this.state.markLevelCompleted(
            this.state.currentMajorLevel, 
            this.state.currentMinorLevel, 
            finalScore
        );
        this.state.saveTotalScore();
        
        // 显示通关界面
        this.showVictoryScreen(timeUsed, finalScore, bonusScore);
    }
    
    // 显示通关界面
    showVictoryScreen(timeUsed, levelScore, bonusScore) {
        document.getElementById('victory-title').textContent = 
            this.state.currentMinorLevel === GameConfig.MINOR_LEVELS ? 
            `第${this.state.currentMajorLevel}大关通关！` : '恭喜通关！';
        
        document.getElementById('time-used').textContent = timeUsed;
        document.getElementById('level-score').textContent = this.state.currentLevelScore;
        document.getElementById('bonus-score').textContent = bonusScore;
        document.getElementById('final-score').textContent = this.state.totalScore;
        
        // 检查是否还有下一关
        const hasNextLevel = this.hasNextLevel();
        document.getElementById('next-level').style.display = hasNextLevel ? 'block' : 'none';
        
        this.showScreen('victory-screen');
    }
    
    // 检查是否有下一关
    hasNextLevel() {
        if (this.state.currentMinorLevel < GameConfig.MINOR_LEVELS) {
            return true;
        }
        return this.state.currentMajorLevel < GameConfig.MAJOR_LEVELS;
    }
    
    // 下一关
    nextLevel() {
        let nextMajor = this.state.currentMajorLevel;
        let nextMinor = this.state.currentMinorLevel + 1;
        
        if (nextMinor > GameConfig.MINOR_LEVELS) {
            nextMajor++;
            nextMinor = 1;
        }
        
        if (nextMajor <= GameConfig.MAJOR_LEVELS) {
            this.startLevel(nextMajor, nextMinor);
        } else {
            this.backToMenu();
        }
    }
    
    // 游戏结束
    gameOver() {
        this.state.isGameActive = false;
        this.stopTimer();
        this.showScreen('game-over-screen');
    }
    
    // 暂停游戏
    pauseGame() {
        if (this.state.isGameActive) {
            this.state.isPaused = true;
            this.showScreen('pause-screen');
        }
    }
    
    // 恢复游戏
    resumeGame() {
        this.state.isPaused = false;
        this.showScreen('game-screen');
    }
    
    // 重新开始关卡
    restartLevel() {
        this.stopTimer();
        this.startLevel(this.state.currentMajorLevel, this.state.currentMinorLevel);
    }
    
    // 返回主菜单
    backToMenu() {
        this.stopTimer();
        this.state.isGameActive = false;
        this.state.isPaused = false;
        this.updateUI();
        this.generateLevelButtons();
        this.showScreen('main-menu');
    }
    
    // 显示指定屏幕
    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
    }
    
    // 更新UI
    updateUI() {
        document.getElementById('total-score').textContent = this.state.totalScore;
    }
    
    // 更新游戏UI
    updateGameUI() {
        document.getElementById('current-major').textContent = this.state.currentMajorLevel;
        document.getElementById('current-minor').textContent = this.state.currentMinorLevel;
        document.getElementById('current-score').textContent = this.state.currentLevelScore;
        document.getElementById('found-count').textContent = this.state.foundDifferences.length;
        document.getElementById('total-differences').textContent = GameConfig.DIFFERENCES_PER_LEVEL;
        document.getElementById('hint-count').textContent = this.state.hintsRemaining;
        
        // 更新提示按钮状态
        const hintBtn = document.getElementById('hint-btn');
        hintBtn.disabled = this.state.hintsRemaining <= 0 || this.state.currentDifferences.length === 0;
    }
    
    // 更新计时器显示
    updateTimerDisplay() {
        const countdownElement = document.getElementById('countdown');
        countdownElement.textContent = this.state.timeRemaining;
        
        // 时间警告效果
        const timerElement = document.querySelector('.timer');
        if (this.state.timeRemaining <= 10) {
            timerElement.classList.add('warning');
        } else {
            timerElement.classList.remove('warning');
        }
    }
}

// 游戏初始化
document.addEventListener('DOMContentLoaded', () => {
    window.game = new SpotTheDifferenceGame();
});
