# 找茬小游戏

## 游戏特色

- **5大关卡**：每大关包含5小关，共25个挑战关卡
- **倒计时挑战**：第1关60秒，第2关50秒，第3关40秒，第4关30秒，第5关20秒
- **积分系统**：完成关卡获得积分，大关通关额外奖励
- **响应式设计**：支持PC和移动端
- **触摸支持**：完美支持手机触摸操作

## 最近修复的问题

### 1. 移动端点击位置不准确
- **问题**：手机触摸时点击位置与实际差异位置不匹配
- **解决方案**：
  - 添加了触摸事件支持
  - 修复了坐标转换逻辑
  - 改进了画布缩放计算

### 2. 差异检测不准确
- **问题**：点击差异区域无法正确识别
- **解决方案**：
  - 重新设计了差异区域坐标计算
  - 使用动态容错范围
  - 改进了点击检测算法

### 3. 提示功能位置错误
- **问题**：提示标记显示位置不正确
- **解决方案**：
  - 修复了提示坐标转换
  - 添加了正确的缩放计算
  - 改进了视觉效果

## 游戏玩法

1. **选择关卡**：从主菜单选择要挑战的关卡
2. **找出差异**：在规定时间内找出两张图片的5个不同之处
3. **获得积分**：每找到一个差异获得积分，剩余时间越多奖励越高
4. **使用提示**：每关有3次提示机会，但会扣除积分
5. **通关奖励**：完成大关获得额外奖励积分

## 技术特点

- **纯前端实现**：HTML5 + CSS3 + JavaScript
- **Canvas绘图**：使用Canvas API生成游戏图片
- **本地存储**：自动保存游戏进度和积分
- **响应式布局**：适配各种屏幕尺寸
- **触摸优化**：专门优化移动端体验

## 文件结构

```
├── index.html          # 主页面
├── css/
│   └── style.css      # 样式文件
├── js/
│   ├── game.js        # 游戏主逻辑
│   └── utils.js       # 工具函数
└── README.md          # 说明文档
```

## 如何运行

1. 启动本地服务器：
   ```bash
   python -m http.server 8000
   ```

2. 在浏览器中访问：
   ```
   http://localhost:8000
   ```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 更新日志

### v1.1 (当前版本)
- ✅ 修复移动端触摸点击问题
- ✅ 改进差异检测精度
- ✅ 优化提示功能显示
- ✅ 添加错误点击反馈
- ✅ 改进响应式设计

### v1.0
- ✅ 基础游戏功能
- ✅ 5大关25小关
- ✅ 倒计时系统
- ✅ 积分系统
- ✅ 本地存储
